{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 1, "numPendingTestSuites": 11, "numPendingTests": 93, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 12, "numTotalTests": 94, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750459881482, "success": true, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 11, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883280, "runtime": 1427, "slow": false, "start": 1750459881853}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js", "testResults": [{"ancestorTitles": ["SessionService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Service Initialization should export a service instance", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should export a service instance"}, {"ancestorTitles": ["SessionService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Service Initialization should have required methods", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have required methods"}, {"ancestorTitles": ["SessionService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Service Initialization should have Redis client", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have Redis client"}, {"ancestorTitles": ["SessionService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Service Initialization should have session queues map", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have session queues map"}, {"ancestorTitles": ["SessionService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Basic Functionality should be able to call initializeContext method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call initializeContext method"}, {"ancestorTitles": ["SessionService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Basic Functionality should be able to call createSession method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call createSession method"}, {"ancestorTitles": ["SessionService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Basic Functionality should be able to call getSession method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call getSession method"}, {"ancestorTitles": ["SessionService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Basic Functionality should be able to call updateSession method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call updateSession method"}, {"ancestorTitles": ["SessionService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Basic Functionality should be able to call deleteSession method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call deleteSession method"}, {"ancestorTitles": ["SessionService", "Context Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Context Initialization should initialize context with proper structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should initialize context with proper structure"}, {"ancestorTitles": ["SessionService", "Context Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionService Context Initialization should merge custom context with defaults", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should merge custom context with defaults"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 9, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883283, "runtime": 1416, "slow": false, "start": 1750459881867}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js", "testResults": [{"ancestorTitles": ["RestaurantStore", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Service Initialization should export a service instance", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should export a service instance"}, {"ancestorTitles": ["RestaurantStore", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Service Initialization should have required methods", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have required methods"}, {"ancestorTitles": ["RestaurantStore", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Service Initialization should have data storage maps", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have data storage maps"}, {"ancestorTitles": ["RestaurantStore", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Basic Functionality should be able to call initialize method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call initialize method"}, {"ancestorTitles": ["RestaurantStore", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Basic Functionality should be able to call getBrandRef method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call getBrandRef method"}, {"ancestorTitles": ["RestaurantStore", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Basic Functionality should be able to call getRestaurantRef method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call getRestaurantRef method"}, {"ancestorTitles": ["RestaurantStore", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Basic Functionality should be able to call getRestaurantsByBrand method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call getRestaurantsByBrand method"}, {"ancestorTitles": ["RestaurantStore", "Data Storage"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Data Storage should have proper data structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have proper data structure"}, {"ancestorTitles": ["RestaurantStore", "Data Storage"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "RestaurantStore Data Storage should handle empty data gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty data gracefully"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 6, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883305, "runtime": 1439, "slow": false, "start": 1750459881866}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js", "testResults": [{"ancestorTitles": ["LinkGenerator", "generateMenuLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateMenuLink() should generate menu link with token and orderURL", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate menu link with token and orderURL"}, {"ancestorTitles": ["LinkGenerator", "generateMenuLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateMenuLink() should handle different domains", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle different domains"}, {"ancestorTitles": ["LinkGenerator", "generateMenuLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateMenuLink() should handle special characters in token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle special characters in token"}, {"ancestorTitles": ["LinkGenerator", "generateAddressLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateAddressLink() should generate address link with token and orderURL", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate address link with token and orderURL"}, {"ancestorTitles": ["LinkGenerator", "generateAddressLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateAddressLink() should handle different domains", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle different domains"}, {"ancestorTitles": ["LinkGenerator", "generateAddressLink()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "LinkGenerator generateAddressLink() should handle special characters in token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle special characters in token"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 15, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883362, "runtime": 1529, "slow": false, "start": 1750459881833}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js", "testResults": [{"ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator generateOpaqueToken() should generate an opaque token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate an opaque token"}, {"ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator generateOpaqueToken() should generate unique tokens", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate unique tokens"}, {"ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator generateOpaqueToken() should generate tokens with base64url format", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate tokens with base64url format"}, {"ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator generateOpaqueToken() should generate tokens of consistent length", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate tokens of consistent length"}, {"ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator generateOpaqueToken() should handle errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle errors gracefully"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should validate a valid token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should validate a valid token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject null token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject null token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject undefined token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject undefined token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject non-string token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject non-string token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject empty string token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject empty string token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject invalid base64url token", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject invalid base64url token"}, {"ancestorTitles": ["SessionIdGenerator", "validateToken()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator validateToken() should reject token with wrong length", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject token with wrong length"}, {"ancestorTitles": ["SessionIdGenerator", "Token Security"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator Token Security should generate cryptographically secure tokens", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate cryptographically secure tokens"}, {"ancestorTitles": ["SessionIdGenerator", "Token Security"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator Token Security should generate tokens that are URL-safe", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate tokens that are URL-safe"}, {"ancestorTitles": ["SessionIdGenerator", "Token Security"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "SessionIdGenerator Token Security should generate tokens without padding", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should generate tokens without padding"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 16, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883472, "runtime": 1561, "slow": false, "start": 1750459881911}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js", "testResults": [{"ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildBasicTextMessageData() should build basic text message for notification", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should build basic text message for notification"}, {"ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildBasicTextMessageData() should build basic text message for dialog", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should build basic text message for dialog"}, {"ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildBasicTextMessageData() should default to notification message type", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should default to notification message type"}, {"ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildBasicTextMessageData() should handle empty text message", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty text message"}, {"ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildBasicTextMessageData() should handle special characters in text", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle special characters in text"}, {"ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildQuickReplyMessageData() should build quick reply message with buttons", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should build quick reply message with buttons"}, {"ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildQuickReplyMessageData() should handle single button", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle single button"}, {"ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildQuickReplyMessageData() should handle header and footer", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle header and footer"}, {"ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildQuickReplyMessageData() should throw error for invalid button count", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should throw error for invalid button count"}, {"ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildWhatsAppTemplateMessageData() should build template message with variables", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should build template message with variables"}, {"ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildWhatsAppTemplateMessageData() should handle template with image", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle template with image"}, {"ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildWhatsAppTemplateMessageData() should handle empty options", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty options"}, {"ancestorTitles": ["MessageBuilders", "buildDialogueMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildDialogueMessageData() should build dialogue text message", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should build dialogue text message"}, {"ancestorTitles": ["MessageBuilders", "buildDialogueMessageData()"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders buildDialogueMessageData() should handle empty text", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty text"}, {"ancestorTitles": ["MessageBuilders", "Message Structure Validation"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders Message Structure Validation should create valid notification message structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create valid notification message structure"}, {"ancestorTitles": ["MessageBuilders", "Message Structure Validation"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "MessageBuilders Message Structure Validation should create valid dialogue message structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create valid dialogue message structure"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 1, "numPendingTests": 7, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883547, "runtime": 1680, "slow": false, "start": 1750459881867}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js", "testResults": [{"ancestorTitles": ["WhatsAppService", "Service Initialization"], "duration": 299, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Service Initialization should export a service instance", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should export a service instance"}, {"ancestorTitles": ["WhatsAppService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Service Initialization should have required methods", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have required methods"}, {"ancestorTitles": ["WhatsAppService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Service Initialization should have configuration properties or be configurable", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have configuration properties or be configurable"}, {"ancestorTitles": ["WhatsAppService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Service Initialization should have message queue or queueing capability", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have message queue or queueing capability"}, {"ancestorTitles": ["WhatsAppService", "Service Initialization"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Service Initialization should have retry configuration or error handling", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have retry configuration or error handling"}, {"ancestorTitles": ["WhatsAppService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Basic Functionality should be able to call getAccessToken method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call getAccessToken method"}, {"ancestorTitles": ["WhatsAppService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Basic Functionality should be able to call sendBasicText method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call sendBasicText method"}, {"ancestorTitles": ["WhatsAppService", "Basic Functionality"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsAppService Basic Functionality should be able to call sendQuickReply method", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should be able to call sendQuickReply method"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 5, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883839, "runtime": 2028, "slow": false, "start": 1750459881811}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js", "testResults": [{"ancestorTitles": ["Order Model - refundStatus Simple Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Simple Tests should verify refundStatus field exists in schema", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify refundStatus field exists in schema"}, {"ancestorTitles": ["Order Model - refundStatus Simple Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Simple Tests should create order with minimal data and verify refundStatus default", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create order with minimal data and verify refundStatus default"}, {"ancestorTitles": ["Order Model - refundStatus Simple Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Simple Tests should verify refundStatus can be set to valid enum values", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify refundStatus can be set to valid enum values"}, {"ancestorTitles": ["Order Model - refundStatus Simple Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Simple Tests should verify Order model has all refund-related fields", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify Order model has all refund-related fields"}, {"ancestorTitles": ["Order Model - refundStatus Simple Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Simple Tests should demonstrate refundStatus functionality without complex ObjectIds", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should demonstrate refundStatus functionality without complex ObjectIds"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 4, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883860, "runtime": 2012, "slow": false, "start": 1750459881848}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js", "testResults": [{"ancestorTitles": ["Order Model - refundStatus Field Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Field Tests should have refundStatus default value as NONE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should have refundStatus default value as NONE"}, {"ancestorTitles": ["Order Model - refundStatus Field Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Field Tests should allow valid refundStatus enum values", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should allow valid refundStatus enum values"}, {"ancestorTitles": ["Order Model - refundStatus Field Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Field Tests should reject invalid refundStatus values", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject invalid refundStatus values"}, {"ancestorTitles": ["Order Model - refundStatus Field Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Field Tests should verify refundStatus field definition in schema", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify refundStatus field definition in schema"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 3, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459883874, "runtime": 2042, "slow": false, "start": 1750459881832}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js", "testResults": [{"ancestorTitles": ["Order Model - refundStatus Advanced Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Advanced Tests R-STATUS-004: should update refundStatus to FULL after full refund", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "R-STATUS-004: should update refundStatus to FULL after full refund"}, {"ancestorTitles": ["Order Model - refundStatus Advanced Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Advanced Tests R-STATUS-006: should support GraphQL refundStatus field queries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "R-STATUS-006: should support GraphQL refundStatus field queries"}, {"ancestorTitles": ["Order Model - refundStatus Advanced Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order Model - refundStatus Advanced Tests R-STATUS-007: should maintain consistency between refundStatus and totalRefunded", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "R-STATUS-007: should maintain consistency between refundStatus and totalRefunded"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 5, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459884059, "runtime": 2218, "slow": false, "start": 1750459881841}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js", "testResults": [{"ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order FSM Actions sendWelcomeMessage should send welcome message with restaurant info and buttons", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should send welcome message with restaurant info and buttons"}, {"ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order FSM Actions sendWelcomeMessage should handle missing restaurant selection", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle missing restaurant selection"}, {"ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order FSM Actions sendWelcomeMessage should throw error with invalid context", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should throw error with invalid context"}, {"ancestorTitles": ["Order FSM Actions", "sendPaymentLink"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order FSM Actions sendPaymentLink should successfully send payment link", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should successfully send payment link"}, {"ancestorTitles": ["Order FSM Actions", "sendPaymentLink"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order FSM Actions sendPaymentLink should handle missing parameters", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle missing parameters"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 6, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459884109, "runtime": 2233, "slow": false, "start": 1750459881876}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js", "testResults": [{"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should pass validation with valid token and session", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should pass validation with valid token and session"}, {"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should return 401 when token is missing", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return 401 when token is missing"}, {"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should return 401 when token format is invalid", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return 401 when token format is invalid"}, {"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should return 404 when session does not exist", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return 404 when session does not exist"}, {"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should return 404 when session exists but dialogueId is missing", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return 404 when session exists but dialogueId is missing"}, {"ancestorTitles": ["Session Validator Middleware"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Session Validator Middleware should return 500 when internal error occurs", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return 500 when internal error occurs"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 6, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750459884759, "runtime": 2919, "slow": false, "start": 1750459881840}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js", "testResults": [{"ancestorTitles": ["Dialog Manager", "基础状态转换测试"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 基础状态转换测试 should transition from initial to restaurant selection on message received", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should transition from initial to restaurant selection on message received"}, {"ancestorTitles": ["Dialog Manager", "基础状态转换测试"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 基础状态转换测试 should transition through the order flow", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should transition through the order flow"}, {"ancestorTitles": ["Dialog Manager", "基础状态转换测试"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 基础状态转换测试 should handle payment failure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle payment failure"}, {"ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 状态机服务生命周期管理 should start and stop dialog manager service", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should start and stop dialog manager service"}, {"ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 状态机服务生命周期管理 should properly clean up mocks and reset state", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should properly clean up mocks and reset state"}, {"ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Dialog Manager 状态机服务生命周期管理 should handle unknown event types gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle unknown event types gracefully"}], "displayName": {"name": "UNIT", "color": "blue"}, "failureMessage": null}], "wasInterrupted": false, "processedAt": "2025-06-20T22:51:24.861Z", "environment": {"nodeVersion": "v18.20.5", "platform": "linux", "arch": "x64", "env": "test"}, "summary": {"total": 94, "passed": 1, "failed": 0, "skipped": 93, "duration": 22504, "successRate": "1.06", "byType": {"UNIT": {"total": 94, "passed": 1, "failed": 0, "skipped": 93, "duration": 299}}}, "recommendations": [{"type": "quality", "priority": "high", "message": "测试成功率 1.06% 低于推荐的90%，需要修复失败的测试"}]}