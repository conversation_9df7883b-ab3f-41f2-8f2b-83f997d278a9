<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Firespoon API Tests" tests="94" failures="0" errors="0" time="3.354">
  <testsuite name="SessionService" errors="0" failures="0" skipped="11" timestamp="2025-06-20T22:51:21" time="1.427" tests="11">
    <testcase classname="UNIT.SessionService › Service Initialization" name="should export a service instance" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Service Initialization" name="should have required methods" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Service Initialization" name="should have Redis client" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Service Initialization" name="should have session queues map" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Basic Functionality" name="should be able to call initializeContext method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Basic Functionality" name="should be able to call createSession method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Basic Functionality" name="should be able to call getSession method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Basic Functionality" name="should be able to call updateSession method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Basic Functionality" name="should be able to call deleteSession method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Context Initialization" name="should initialize context with proper structure" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionService › Context Initialization" name="should merge custom context with defaults" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="RestaurantStore" errors="0" failures="0" skipped="9" timestamp="2025-06-20T22:51:21" time="1.416" tests="9">
    <testcase classname="UNIT.RestaurantStore › Service Initialization" name="should export a service instance" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Service Initialization" name="should have required methods" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Service Initialization" name="should have data storage maps" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Basic Functionality" name="should be able to call initialize method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Basic Functionality" name="should be able to call getBrandRef method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Basic Functionality" name="should be able to call getRestaurantRef method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Basic Functionality" name="should be able to call getRestaurantsByBrand method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Data Storage" name="should have proper data structure" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.RestaurantStore › Data Storage" name="should handle empty data gracefully" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="LinkGenerator" errors="0" failures="0" skipped="6" timestamp="2025-06-20T22:51:21" time="1.439" tests="6">
    <testcase classname="UNIT.LinkGenerator › generateMenuLink()" name="should generate menu link with token and orderURL" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.LinkGenerator › generateMenuLink()" name="should handle different domains" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.LinkGenerator › generateMenuLink()" name="should handle special characters in token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.LinkGenerator › generateAddressLink()" name="should generate address link with token and orderURL" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.LinkGenerator › generateAddressLink()" name="should handle different domains" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.LinkGenerator › generateAddressLink()" name="should handle special characters in token" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="SessionIdGenerator" errors="0" failures="0" skipped="15" timestamp="2025-06-20T22:51:21" time="1.529" tests="15">
    <testcase classname="UNIT.SessionIdGenerator › generateOpaqueToken()" name="should generate an opaque token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › generateOpaqueToken()" name="should generate unique tokens" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › generateOpaqueToken()" name="should generate tokens with base64url format" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › generateOpaqueToken()" name="should generate tokens of consistent length" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › generateOpaqueToken()" name="should handle errors gracefully" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should validate a valid token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject null token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject undefined token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject non-string token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject empty string token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject invalid base64url token" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › validateToken()" name="should reject token with wrong length" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › Token Security" name="should generate cryptographically secure tokens" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › Token Security" name="should generate tokens that are URL-safe" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.SessionIdGenerator › Token Security" name="should generate tokens without padding" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="MessageBuilders" errors="0" failures="0" skipped="16" timestamp="2025-06-20T22:51:21" time="1.561" tests="16">
    <testcase classname="UNIT.MessageBuilders › buildBasicTextMessageData()" name="should build basic text message for notification" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildBasicTextMessageData()" name="should build basic text message for dialog" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildBasicTextMessageData()" name="should default to notification message type" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildBasicTextMessageData()" name="should handle empty text message" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildBasicTextMessageData()" name="should handle special characters in text" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildQuickReplyMessageData()" name="should build quick reply message with buttons" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildQuickReplyMessageData()" name="should handle single button" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildQuickReplyMessageData()" name="should handle header and footer" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildQuickReplyMessageData()" name="should throw error for invalid button count" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildWhatsAppTemplateMessageData()" name="should build template message with variables" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildWhatsAppTemplateMessageData()" name="should handle template with image" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildWhatsAppTemplateMessageData()" name="should handle empty options" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildDialogueMessageData()" name="should build dialogue text message" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › buildDialogueMessageData()" name="should handle empty text" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › Message Structure Validation" name="should create valid notification message structure" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.MessageBuilders › Message Structure Validation" name="should create valid dialogue message structure" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="WhatsAppService" errors="0" failures="0" skipped="7" timestamp="2025-06-20T22:51:21" time="1.68" tests="8">
    <testcase classname="UNIT.WhatsAppService › Service Initialization" name="should export a service instance" time="0.299">
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Service Initialization" name="should have required methods" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Service Initialization" name="should have configuration properties or be configurable" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Service Initialization" name="should have message queue or queueing capability" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Service Initialization" name="should have retry configuration or error handling" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Basic Functionality" name="should be able to call getAccessToken method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Basic Functionality" name="should be able to call sendBasicText method" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.WhatsAppService › Basic Functionality" name="should be able to call sendQuickReply method" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order Model - refundStatus Simple Tests" errors="0" failures="0" skipped="5" timestamp="2025-06-20T22:51:21" time="2.028" tests="5">
    <testcase classname="UNIT.Order Model - refundStatus Simple Tests" name="should verify refundStatus field exists in schema" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Simple Tests" name="should create order with minimal data and verify refundStatus default" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Simple Tests" name="should verify refundStatus can be set to valid enum values" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Simple Tests" name="should verify Order model has all refund-related fields" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Simple Tests" name="should demonstrate refundStatus functionality without complex ObjectIds" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order Model - refundStatus Field Tests" errors="0" failures="0" skipped="4" timestamp="2025-06-20T22:51:21" time="2.012" tests="4">
    <testcase classname="UNIT.Order Model - refundStatus Field Tests" name="should have refundStatus default value as NONE" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Field Tests" name="should allow valid refundStatus enum values" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Field Tests" name="should reject invalid refundStatus values" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Field Tests" name="should verify refundStatus field definition in schema" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order Model - refundStatus Advanced Tests" errors="0" failures="0" skipped="3" timestamp="2025-06-20T22:51:21" time="2.042" tests="3">
    <testcase classname="UNIT.Order Model - refundStatus Advanced Tests" name="R-STATUS-004: should update refundStatus to FULL after full refund" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Advanced Tests" name="R-STATUS-006: should support GraphQL refundStatus field queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order Model - refundStatus Advanced Tests" name="R-STATUS-007: should maintain consistency between refundStatus and totalRefunded" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order FSM Actions" errors="0" failures="0" skipped="5" timestamp="2025-06-20T22:51:21" time="2.218" tests="5">
    <testcase classname="UNIT.Order FSM Actions › sendWelcomeMessage" name="should send welcome message with restaurant info and buttons" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order FSM Actions › sendWelcomeMessage" name="should handle missing restaurant selection" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order FSM Actions › sendWelcomeMessage" name="should throw error with invalid context" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order FSM Actions › sendPaymentLink" name="should successfully send payment link" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Order FSM Actions › sendPaymentLink" name="should handle missing parameters" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Session Validator Middleware" errors="0" failures="0" skipped="6" timestamp="2025-06-20T22:51:21" time="2.233" tests="6">
    <testcase classname="UNIT.Session Validator Middleware" name="should pass validation with valid token and session" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Session Validator Middleware" name="should return 401 when token is missing" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Session Validator Middleware" name="should return 401 when token format is invalid" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Session Validator Middleware" name="should return 404 when session does not exist" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Session Validator Middleware" name="should return 404 when session exists but dialogueId is missing" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Session Validator Middleware" name="should return 500 when internal error occurs" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Dialog Manager" errors="0" failures="0" skipped="6" timestamp="2025-06-20T22:51:21" time="2.919" tests="6">
    <testcase classname="UNIT.Dialog Manager › 基础状态转换测试" name="should transition from initial to restaurant selection on message received" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Dialog Manager › 基础状态转换测试" name="should transition through the order flow" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Dialog Manager › 基础状态转换测试" name="should handle payment failure" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Dialog Manager › 状态机服务生命周期管理" name="should start and stop dialog manager service" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Dialog Manager › 状态机服务生命周期管理" name="should properly clean up mocks and reset state" time="0">
      <skipped/>
    </testcase>
    <testcase classname="UNIT.Dialog Manager › 状态机服务生命周期管理" name="should handle unknown event types gracefully" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>