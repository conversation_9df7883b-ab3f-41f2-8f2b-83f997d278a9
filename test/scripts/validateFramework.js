#!/usr/bin/env node

/**
 * 测试框架验证脚本
 * 验证统一测试框架是否符合SOP要求
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class FrameworkValidator {
  constructor() {
    this.results = {
      configValidation: false,
      unitTestExecution: false,
      integrationTestExecution: false,
      reportGeneration: false,
      outputFormat: false,
      errors: []
    };
  }

  async validate() {
    console.log('🔍 开始验证测试框架...\n');

    try {
      await this.validateConfig();
      await this.validateUnitTests();
      await this.validateIntegrationTests();
      await this.validateReports();
      await this.validateOutputFormat();
      
      this.printSummary();
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  async validateConfig() {
    console.log('📋 验证配置文件...');
    
    try {
      // 检查Jest配置文件
      const configPath = path.join(__dirname, '../../test/config/jest.config.js');
      if (!fs.existsSync(configPath)) {
        throw new Error('Jest配置文件不存在');
      }
      
      const config = require(configPath);
      
      // 验证多项目配置
      if (!config.projects || config.projects.length < 4) {
        throw new Error('缺少必要的项目配置 (UNIT, INTEGRATION, E2E, PERFORMANCE)');
      }
      
      const projectNames = config.projects.map(p => p.displayName?.name);
      const requiredProjects = ['UNIT', 'INTEGRATION', 'E2E', 'PERFORMANCE'];
      
      for (const required of requiredProjects) {
        if (!projectNames.includes(required)) {
          throw new Error(`缺少 ${required} 项目配置`);
        }
      }
      
      // 验证报告器配置
      if (!config.reporters || !config.reporters.some(r => Array.isArray(r) && r[0].includes('customReporter'))) {
        throw new Error('缺少自定义报告器配置');
      }
      
      this.results.configValidation = true;
      console.log('✅ 配置文件验证通过\n');
    } catch (error) {
      this.results.errors.push(`配置验证失败: ${error.message}`);
      console.log('❌ 配置文件验证失败\n');
    }
  }

  async validateUnitTests() {
    console.log('🔧 验证单元测试执行...');
    
    try {
      const output = execSync('npm run test:unit -- --testNamePattern="WhatsAppService" --silent', {
        encoding: 'utf8',
        cwd: path.join(__dirname, '../..'),
        timeout: 30000
      });
      
      // 检查输出是否包含SOP要求的格式
      if (output.includes('FIRESPOON API 测试执行报告') && 
          output.includes('CaseID:') && 
          output.includes('Module:') && 
          output.includes('Description:')) {
        this.results.unitTestExecution = true;
        console.log('✅ 单元测试执行验证通过\n');
      } else {
        throw new Error('输出格式不符合SOP要求');
      }
    } catch (error) {
      this.results.errors.push(`单元测试验证失败: ${error.message}`);
      console.log('❌ 单元测试执行验证失败\n');
    }
  }

  async validateIntegrationTests() {
    console.log('🔗 验证集成测试执行...');
    
    try {
      // 运行一个简单的集成测试
      const output = execSync('npm run test:integration -- --testNamePattern="GraphQL.*schema" --silent --maxWorkers=1', {
        encoding: 'utf8',
        cwd: path.join(__dirname, '../..'),
        timeout: 60000
      });
      
      if (output.includes('INTEGRATION 测试结果') && output.includes('✅ PASS')) {
        this.results.integrationTestExecution = true;
        console.log('✅ 集成测试执行验证通过\n');
      } else {
        throw new Error('集成测试输出格式不正确');
      }
    } catch (error) {
      this.results.errors.push(`集成测试验证失败: ${error.message}`);
      console.log('❌ 集成测试执行验证失败\n');
    }
  }

  async validateReports() {
    console.log('📄 验证报告生成...');
    
    try {
      const reportsDir = path.join(__dirname, '../../test/reports');
      
      // 检查报告目录
      if (!fs.existsSync(reportsDir)) {
        throw new Error('报告目录不存在');
      }
      
      // 检查必要的报告文件
      const requiredFiles = [
        'test-summary.json',
        'test-report.md',
        'processed-results.json'
      ];
      
      for (const file of requiredFiles) {
        const filePath = path.join(reportsDir, file);
        if (!fs.existsSync(filePath)) {
          throw new Error(`缺少报告文件: ${file}`);
        }
      }
      
      // 验证报告内容
      const summaryPath = path.join(reportsDir, 'test-summary.json');
      const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
      
      if (!summary.summary || !summary.projects) {
        throw new Error('报告格式不正确');
      }
      
      this.results.reportGeneration = true;
      console.log('✅ 报告生成验证通过\n');
    } catch (error) {
      this.results.errors.push(`报告验证失败: ${error.message}`);
      console.log('❌ 报告生成验证失败\n');
    }
  }

  async validateOutputFormat() {
    console.log('📋 验证输出格式...');
    
    try {
      const reportPath = path.join(__dirname, '../../test/reports/test-report.md');
      const reportContent = fs.readFileSync(reportPath, 'utf8');
      
      // 检查SOP要求的格式元素
      const requiredElements = [
        '# Firespoon API 测试报告',
        '## 📊 测试概览',
        '## 📈 测试结果',
        '## 🔍 分类测试结果',
        '## 💡 改进建议'
      ];
      
      for (const element of requiredElements) {
        if (!reportContent.includes(element)) {
          throw new Error(`缺少必要的报告元素: ${element}`);
        }
      }
      
      this.results.outputFormat = true;
      console.log('✅ 输出格式验证通过\n');
    } catch (error) {
      this.results.errors.push(`输出格式验证失败: ${error.message}`);
      console.log('❌ 输出格式验证失败\n');
    }
  }

  printSummary() {
    console.log('=' * 60);
    console.log('📊 测试框架验证结果总结');
    console.log('=' * 60);
    
    const checks = [
      { name: '配置文件验证', status: this.results.configValidation },
      { name: '单元测试执行', status: this.results.unitTestExecution },
      { name: '集成测试执行', status: this.results.integrationTestExecution },
      { name: '报告生成', status: this.results.reportGeneration },
      { name: '输出格式', status: this.results.outputFormat }
    ];
    
    checks.forEach(check => {
      const icon = check.status ? '✅' : '❌';
      console.log(`${icon} ${check.name}: ${check.status ? 'PASS' : 'FAIL'}`);
    });
    
    const passedCount = checks.filter(c => c.status).length;
    const totalCount = checks.length;
    const successRate = ((passedCount / totalCount) * 100).toFixed(2);
    
    console.log(`\n🎯 验证成功率: ${successRate}% (${passedCount}/${totalCount})`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ 发现的问题:');
      this.results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n' + '=' * 60);
    
    if (passedCount === totalCount) {
      console.log('🎉 测试框架验证完全通过！框架符合SOP要求。');
      process.exit(0);
    } else {
      console.log('💥 测试框架验证未完全通过，请修复上述问题。');
      process.exit(1);
    }
  }
}

// 运行验证
if (require.main === module) {
  const validator = new FrameworkValidator();
  validator.validate().catch(error => {
    console.error('验证过程异常:', error);
    process.exit(1);
  });
}

module.exports = FrameworkValidator;
