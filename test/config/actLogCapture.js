/**
 * Act Log捕获器
 * 捕获测试执行过程中的所有程序输出
 */

class ActLogCapture {
  constructor() {
    this.capturedLogs = [];
    this.originalConsole = {};
    this.originalProcess = {};
    this.originalWrite = {};
    this.isCapturing = false;
    this.startTime = Date.now();
  }

  startCapture() {
    if (this.isCapturing) return;

    this.startTime = Date.now();
    this.capturedLogs = [];

    // 保存原始console方法
    this.originalConsole.log = console.log;
    this.originalConsole.error = console.error;
    this.originalConsole.warn = console.warn;
    this.originalConsole.info = console.info;
    this.originalConsole.debug = console.debug;

    // 拦截console方法
    console.log = (...args) => this.captureLog('console.log', args);
    console.error = (...args) => this.captureLog('console.error', args);
    console.warn = (...args) => this.captureLog('console.warn', args);
    console.info = (...args) => this.captureLog('console.info', args);
    console.debug = (...args) => this.captureLog('console.debug', args);

    // 拦截process输出流
    this.interceptProcessOutput();

    // 设置异常处理
    this.setupExceptionHandling();

    this.isCapturing = true;
  }

  stopCapture() {
    if (!this.isCapturing) return [];

    // 恢复原始console方法
    console.log = this.originalConsole.log;
    console.error = this.originalConsole.error;
    console.warn = this.originalConsole.warn;
    console.info = this.originalConsole.info;
    console.debug = this.originalConsole.debug;

    // 恢复process输出流
    this.restoreProcessOutput();

    // 移除异常处理
    this.removeExceptionHandling();

    this.isCapturing = false;
    return [...this.capturedLogs];
  }

  captureLog(type, args) {
    const timestamp = Date.now() - this.startTime;
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    this.capturedLogs.push({
      type,
      message,
      timestamp,
      args: args
    });

    // 同时输出到原始console (可选)
    if (this.originalConsole[type.split('.')[1]]) {
      this.originalConsole[type.split('.')[1]](...args);
    }
  }

  interceptProcessOutput() {
    // 拦截stdout
    this.originalWrite.stdout = process.stdout.write;
    process.stdout.write = (chunk, encoding, callback) => {
      this.captureLog('process.stdout', [chunk.toString()]);
      return this.originalWrite.stdout.call(process.stdout, chunk, encoding, callback);
    };

    // 拦截stderr
    this.originalWrite.stderr = process.stderr.write;
    process.stderr.write = (chunk, encoding, callback) => {
      this.captureLog('process.stderr', [chunk.toString()]);
      return this.originalWrite.stderr.call(process.stderr, chunk, encoding, callback);
    };
  }

  restoreProcessOutput() {
    if (this.originalWrite.stdout) {
      process.stdout.write = this.originalWrite.stdout;
    }
    if (this.originalWrite.stderr) {
      process.stderr.write = this.originalWrite.stderr;
    }
  }

  setupExceptionHandling() {
    // 捕获未处理的异常
    this.originalUncaughtException = process.listeners('uncaughtException');
    process.on('uncaughtException', (error) => {
      this.captureException('uncaughtException', error);
    });

    // 捕获未处理的Promise rejection
    this.originalUnhandledRejection = process.listeners('unhandledRejection');
    process.on('unhandledRejection', (reason, promise) => {
      this.captureException('unhandledRejection', reason);
    });
  }

  removeExceptionHandling() {
    // 移除异常监听器
    process.removeAllListeners('uncaughtException');
    process.removeAllListeners('unhandledRejection');

    // 恢复原始监听器
    if (this.originalUncaughtException) {
      this.originalUncaughtException.forEach(listener => {
        process.on('uncaughtException', listener);
      });
    }
    if (this.originalUnhandledRejection) {
      this.originalUnhandledRejection.forEach(listener => {
        process.on('unhandledRejection', listener);
      });
    }
  }

  captureException(type, error) {
    const timestamp = Date.now() - this.startTime;
    
    this.capturedLogs.push({
      type: `exception.${type}`,
      message: error.message || String(error),
      timestamp,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code
      }
    });
  }

  // 捕获logger输出 (winston, bunyan等)
  interceptLogger() {
    try {
      // 尝试拦截winston
      const winston = require('winston');
      if (winston && winston.loggers) {
        // 拦截winston输出
        this.interceptWinston(winston);
      }
    } catch (e) {
      // winston不存在，忽略
    }

    try {
      // 尝试拦截bunyan
      const bunyan = require('bunyan');
      if (bunyan) {
        // 拦截bunyan输出
        this.interceptBunyan(bunyan);
      }
    } catch (e) {
      // bunyan不存在，忽略
    }
  }

  interceptWinston(winston) {
    // 实现winston拦截逻辑
    // 这里可以根据项目实际使用的logger进行定制
  }

  interceptBunyan(bunyan) {
    // 实现bunyan拦截逻辑
    // 这里可以根据项目实际使用的logger进行定制
  }

  // 获取格式化的日志输出
  getFormattedLogs() {
    return this.capturedLogs.map(log => {
      const timeStr = `+${log.timestamp}ms`;
      if (log.error) {
        return `${timeStr} [${log.type}] ${log.message}\n  Stack: ${log.error.stack}`;
      }
      return `${timeStr} [${log.type}] ${log.message}`;
    });
  }

  // 获取统计信息
  getStats() {
    const stats = {
      total: this.capturedLogs.length,
      byType: {}
    };

    this.capturedLogs.forEach(log => {
      const type = log.type.split('.')[0];
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });

    return stats;
  }
}

module.exports = ActLogCapture;
